"""
静态HTML包导出器 - 修复版
使用Puppeteer自动化浏览器，调用原始的导出静态HTML功能
"""

import os
import json
import tempfile
import subprocess
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional

class StaticHtmlExporter:
    """静态HTML包导出器类"""

    def __init__(self, web_assets_dir: str = None):
        """
        初始化导出器

        Args:
            web_assets_dir: web资源目录路径
        """
        self.project_root = Path(__file__).parent.parent
        self.web_assets_dir = web_assets_dir or str(self.project_root / "web-assets")
        self.temp_dir = str(self.project_root / "temp")

        # 浏览器实例复用 - 性能优化
        self._browser_script = None
        self._browser_initialized = False

        # 确保临时目录存在
        os.makedirs(self.temp_dir, exist_ok=True)
    
    def create_export_script(self, pptx_path: str, output_dir: str, options: Dict[str, Any]) -> str:
        """
        创建Node.js导出脚本
        
        Args:
            pptx_path: PPTX文件路径
            output_dir: 输出目录
            options: 转换选项
            
        Returns:
            脚本文件路径
        """
        
        # 转换路径为绝对路径并标准化
        pptx_path = os.path.abspath(pptx_path).replace('\\', '/')
        output_dir = os.path.abspath(output_dir).replace('\\', '/')
        web_assets_dir = os.path.abspath(self.web_assets_dir).replace('\\', '/')
        html_file_path = f"{web_assets_dir}/index.html"
        
        # 提取PPTX文件名（不含扩展名）
        pptx_filename = os.path.splitext(os.path.basename(pptx_path))[0]
        
        # 创建Node.js脚本内容
        script_content = f"""
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function exportStaticHtml() {{
    console.log('🚀 启动Puppeteer导出静态HTML包...');
    
    const browser = await puppeteer.launch({{
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu'
        ]
    }});
    
    try {{
        const page = await browser.newPage();
        
        // 设置页面大小
        await page.setViewport({{ width: 1920, height: 1080 }});
        
        // 设置超时
        page.setDefaultTimeout(60000);
        
        // 加载转换页面
        const htmlPath = '{html_file_path}';
        console.log('📄 加载页面:', htmlPath);
        
        await page.goto(`file://${{htmlPath}}`);
        
        // 等待页面加载完成
        await page.waitForSelector('#uploadFileInput', {{ timeout: 30000 }});
        console.log('✅ 页面加载完成');
        
        // 读取PPTX文件
        const pptxBuffer = fs.readFileSync('{pptx_path}');
        console.log('📁 读取PPTX文件:', pptxBuffer.length, '字节');
        
        // 在页面中执行转换和导出
        const result = await page.evaluate(async (pptxData, conversionOptions) => {{
            return new Promise((resolve, reject) => {{
                try {{
                    console.log('🔄 开始页面内转换和导出...');
                    
                    // 创建File对象
                    const uint8Array = new Uint8Array(pptxData);
                    const blob = new Blob([uint8Array], {{ 
                        type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' 
                    }});
                    
                    const file = new File([blob], '{pptx_filename}.pptx', {{ 
                        type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' 
                    }});
                    
                    // 获取文件输入元素
                    const fileInput = document.getElementById('uploadFileInput');
                    if (!fileInput) {{
                        reject(new Error('找不到文件输入元素'));
                        return;
                    }}
                    
                    // 模拟文件选择
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);
                    fileInput.files = dataTransfer.files;
                    
                    // 设置转换选项
                    if (conversionOptions.slideMode !== undefined) {{
                        const slideModeCheckbox = document.getElementById('slideMode');
                        if (slideModeCheckbox) {{
                            slideModeCheckbox.checked = conversionOptions.slideMode;
                        }}
                    }}
                    
                    if (conversionOptions.themeProcess !== undefined) {{
                        const themeProcessCheckbox = document.getElementById('themeProcess');
                        if (themeProcessCheckbox) {{
                            themeProcessCheckbox.checked = conversionOptions.themeProcess;
                        }}
                    }}
                    
                    if (conversionOptions.mediaProcess !== undefined) {{
                        const mediaProcessCheckbox = document.getElementById('mediaProcess');
                        if (mediaProcessCheckbox) {{
                            mediaProcessCheckbox.checked = conversionOptions.mediaProcess;
                        }}
                    }}
                    
                    // 监听转换完成事件
                    let conversionCompleted = false;
                    let exportCompleted = false;
                    let zipData = null;
                    
                    // 监听转换完成事件
                    $(document).on('pptxjs:conversion-complete', function() {{
                        console.log('📊 转换完成，开始导出...');
                        conversionCompleted = true;
                        
                        // 显示导出按钮
                        const exportBtn = document.getElementById('exportStaticHtml');
                        if (exportBtn) {{
                            exportBtn.style.display = 'block';
                            
                            // 重写下载函数以捕获ZIP数据 - 优化版
                            const originalDownloadZipFile = window.downloadZipFile;
                            window.downloadZipFile = function(blob, filename) {{
                                console.log('🎯 捕获ZIP文件:', filename, blob.size, '字节');

                                // 优化：使用Base64编码传输，避免大数组转换
                                const reader = new FileReader();
                                reader.onload = function() {{
                                    // 直接使用Base64编码，比Array转换快很多
                                    const base64Data = reader.result.split(',')[1]; // 移除data:前缀

                                    exportCompleted = true;

                                    resolve({{
                                        success: true,
                                        zipDataBase64: base64Data,
                                        filename: filename,
                                        size: blob.size,
                                        message: '静态HTML包导出成功'
                                    }});
                                }};
                                reader.onerror = function() {{
                                    reject(new Error('读取ZIP数据失败'));
                                }};
                                // 使用Base64编码而不是ArrayBuffer
                                reader.readAsDataURL(blob);
                            }};
                            
                            // 触发导出
                            setTimeout(() => {{
                                exportBtn.click();
                            }}, 1000);
                        }} else {{
                            reject(new Error('找不到导出按钮'));
                        }}
                    }});
                    
                    // 触发文件变化事件
                    const changeEvent = new Event('change', {{ bubbles: true }});
                    fileInput.dispatchEvent(changeEvent);
                    
                    console.log('📤 已触发文件上传事件');
                    
                    // 设置超时
                    setTimeout(() => {{
                        if (!conversionCompleted) {{
                            reject(new Error('转换超时 (60秒)'));
                        }} else if (!exportCompleted) {{
                            reject(new Error('导出超时 (60秒)'));
                        }}
                    }}, 60000);
                    
                }} catch (error) {{
                    console.error('页面内处理错误:', error);
                    reject(error);
                }}
            }});
        }}, Array.from(pptxBuffer), {json.dumps(options)});
        
        console.log('✅ 导出完成:', result.success);
        
        if (result.success) {{
            // 保存ZIP文件 - 优化版：直接从Base64解码
            const zipPath = path.join('{output_dir}', result.filename);
            const zipBuffer = Buffer.from(result.zipDataBase64, 'base64');
            fs.writeFileSync(zipPath, zipBuffer);
            
            console.log(JSON.stringify({{
                success: true,
                outputPath: zipPath,
                filename: result.filename,
                size: result.size,
                message: result.message
            }}));
        }} else {{
            console.log(JSON.stringify({{
                success: false,
                error: result.error || '导出失败'
            }}));
        }}
        
    }} catch (error) {{
        console.error('导出过程中发生错误:', error);
        console.log(JSON.stringify({{
            success: false,
            error: error.message
        }}));
    }} finally {{
        await browser.close();
    }}
}}

exportStaticHtml().catch(console.error);
"""
        
        # 创建临时脚本文件
        script_path = os.path.join(self.temp_dir, f"export_{os.getpid()}.js")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_path
    
    async def export_static_html_package(self, pptx_path: str, output_dir: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        导出静态HTML包
        
        Args:
            pptx_path: PPTX文件路径
            output_dir: 输出目录
            options: 转换选项
            
        Returns:
            导出结果字典
        """
        
        if options is None:
            options = {
                "slideMode": True,
                "themeProcess": True,
                "mediaProcess": True
            }
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建导出脚本
        script_path = self.create_export_script(pptx_path, output_dir, options)
        
        try:
            # 运行Node.js脚本
            process = await asyncio.create_subprocess_exec(
                'node', script_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.project_root
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 解析输出
                output_lines = stdout.decode('utf-8').strip().split('\n')
                
                # 查找JSON结果
                for line in reversed(output_lines):
                    try:
                        result = json.loads(line)
                        return result
                    except json.JSONDecodeError:
                        continue
                
                return {
                    "success": False,
                    "error": "无法解析导出结果"
                }
            else:
                error_msg = stderr.decode('utf-8')
                return {
                    "success": False,
                    "error": f"Node.js执行失败: {error_msg}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"执行导出时发生错误: {str(e)}"
            }
        finally:
            # 清理临时脚本
            if os.path.exists(script_path):
                os.unlink(script_path)

# 便捷函数
async def export_pptx_to_static_html(
    file_path: str,
    output_dir: str = None,
    slide_mode: bool = True,
    theme_process: bool = True,
    media_process: bool = True
) -> Dict[str, Any]:
    """
    导出PPTX到静态HTML包的便捷函数
    
    Args:
        file_path: PPTX文件路径
        output_dir: 输出目录（默认为项目根目录下的output）
        slide_mode: 是否启用幻灯片模式
        theme_process: 是否处理主题
        media_process: 是否处理媒体文件
        
    Returns:
        导出结果字典
    """
    
    if output_dir is None:
        project_root = Path(__file__).parent.parent
        output_dir = str(project_root / "output")
    
    options = {
        "slideMode": slide_mode,
        "themeProcess": theme_process,
        "mediaProcess": media_process
    }
    
    exporter = StaticHtmlExporter()
    return await exporter.export_static_html_package(file_path, output_dir, options)
