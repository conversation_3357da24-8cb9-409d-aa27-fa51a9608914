"""
PPTX文件验证模块
"""

import os
import zipfile
from typing import Dict, Any

def validate_pptx_file(file_path: str) -> Dict[str, Any]:
    """
    验证PPTX文件格式和完整性
    
    Args:
        file_path: PPTX文件路径
        
    Returns:
        验证结果字典
    """
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        return {
            "valid": False,
            "error": f"文件不存在: {file_path}"
        }
    
    # 检查文件扩展名
    if not file_path.lower().endswith('.pptx'):
        return {
            "valid": False,
            "error": "文件必须是PPTX格式"
        }
    
    # 检查文件大小
    file_size = os.path.getsize(file_path)
    if file_size == 0:
        return {
            "valid": False,
            "error": "文件为空"
        }
    
    # 检查ZIP结构
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            # 检查必要的PPTX文件结构
            required_files = [
                '[Content_Types].xml',
                '_rels/.rels'
            ]
            
            zip_contents = zip_file.namelist()
            
            for required_file in required_files:
                if required_file not in zip_contents:
                    return {
                        "valid": False,
                        "error": f"缺少必要文件: {required_file}"
                    }
            
            # 检查是否有幻灯片
            slide_files = [f for f in zip_contents if f.startswith('ppt/slides/slide')]
            
            return {
                "valid": True,
                "file_size": file_size,
                "slide_count": len(slide_files),
                "zip_contents": len(zip_contents),
                "message": "PPTX文件验证通过"
            }
            
    except zipfile.BadZipFile:
        return {
            "valid": False,
            "error": "文件不是有效的ZIP格式"
        }
    except Exception as e:
        return {
            "valid": False,
            "error": f"验证过程中发生错误: {str(e)}"
        }
